package internal

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/config"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func ServerRun() {
	cfg := config.LoadConfig()

	// Initialize JWT secret from configuration
	utils.InitJWTSecret(cfg.JWTSecret)

	// Create database connection with proper error handling
	q, db := database.CreateNewDB(cfg.DBString)
	if db == nil {
		log.Fatal("Failed to establish database connection")
	}
	// redis initialization
	pubsubManager, err := wspkg.NewPubSubManager(cfg.RedisURL)
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Printf("Redis PubSub manager initialized successfully")

	wsManager, err := wspkg.DefaultManager()
	if err != nil {
		log.Fatalf("Failed to create WebSocket manager: %v", err)
	}

	e := echo.New()

	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	// e.Use(echoprometheus.NewMiddleware("ws-app"))
	// Configure CORS more securely
	allowedOrigins := []string{"http://localhost:3000", "http://localhost:8080"}
	if corsOrigins := os.Getenv("CORS_ORIGINS"); corsOrigins != "" {
		allowedOrigins = strings.Split(corsOrigins, ",")
	}

	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins:     allowedOrigins,
		AllowMethods:     []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
		AllowHeaders:     []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization, "X-Requested-With"},
		AllowCredentials: true,
		ExposeHeaders:    []string{"Content-Length", "Content-Type", "Authorization"},
		MaxAge:           86400,
	}))

	tokenStore := utils.NewTokenStore(5 * time.Minute)

	wsCtx := wspkg.NewWebSocketCtx(wsManager, q, tokenStore, pubsubManager)
	log.Printf("WebSocketCtx initialized successfully")

	// Register WebSocket routes using WebSocketCtx
	e.POST("/ws-auth", wsCtx.AuthenticateClient)
	e.GET("/ws", wsCtx.DoorClientWS)
	e.POST("/ws-qr-auth", wsCtx.AuthenticateQRClient)
	e.GET("/ws-qr", wsCtx.QRClientWS)

	// Utility routes
	e.GET("/ping", func(c echo.Context) error {
		token := c.QueryParam("token")
		return c.JSON(http.StatusOK, map[string]interface{}{
			"token":     token,
			"status":    "ok",
			"timestamp": time.Now().Unix(),
		})
	})

	e.GET("/redis-stats", func(c echo.Context) error {
		stats := pubsubManager.GetStats()
		return c.JSON(http.StatusOK, stats)
	})

	// httpRoute := httpR.NewHttpRoute(e, hub, q, tokenStore, cfg)
	// httpRoute.RegisterRouter()
	// e.POST("/ws-auth", wsRoute.AuthenticateClient)
	// e.GET("/ws", wsRoute.ClientConnectingHandler)
	// e.POST("/ws-qr-auth", wsRoute.AuthenticateQRClient)
	// e.GET("/ws-qr", wsRoute.QRClientConnectingHandler)
	//
	// e.POST("/pong", wsRoute.ClearHub)
	// e.GET("/get-all-clients", httpRoute.GetConnectedClients)
	// e.POST("/scan-qr", httpRoute.ScanQRCode)
	// e.POST("/open-door", httpRoute.OpenDoor)
	// e.GET("/gen-api-key", httpRoute.GenerateApi)
	//
	// e.DELETE("/delete-tokens-by-client", tokenStore.DeleteTokensByClientID)
	// e.GET("/reset-token-store", tokenStore.ResetTokenStore)
	// e.GET("/get-token-store", tokenStore.GetAllTokens)
	//
	// e.POST("/disconnect-device", wsRoute.RemoveClient)
	// e.GET("/ping", func(c echo.Context) error {
	// 	token := c.QueryParam("token")
	// 	return c.JSON(http.StatusOK, token)
	// })
	// e.GET("/metrics", echoprometheus.NewHandler())

	s := &http.Server{
		Addr:         "0.0.0.0" + cfg.Port,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	e.Server = s

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt)
	defer func() {
		if err := db.Close(); err != nil {
			log.Printf("Error closing database connection: %v", err)
		}
		if err := pubsubManager.Close(); err != nil {
			log.Printf("Error closing Redis PubSub manager: %v", err)
		}
		// if err := wsManager.Close(); err != nil {
		// 	log.Printf("Error closing WebSocket manager: %v", err)
		// }
		tokenStore.Shutdown()
		stop()
	}()
	// Start server
	go func() {
		if err := e.StartServer(s); err != nil && err != http.ErrServerClosed {
			log.Print(err)
			e.Logger.Fatal("shutting down the server")
		}
	}()

	// Wait for interrupt signal to gracefully shut down the server with a timeout of 10 seconds.
	<-ctx.Done()
	e.Logger.Info("Received interrupt signal, shutting down...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := e.Shutdown(ctx); err != nil {
		e.Logger.Fatal(err)
	}
	e.Logger.Info("Server gracefully stopped")
}
