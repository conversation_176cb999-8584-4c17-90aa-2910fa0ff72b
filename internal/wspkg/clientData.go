package wspkg

import (
	"errors"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/google/uuid"
)

type DoorClient struct {
	DoorId       int64
	Name         string
	BranchId     int
	isOpen       bool
	qrIn         *QRClient
	qrOut        *QRClient
	onlineTime   time.Time
	lastCmd      time.Time
	secKey       string
	isprocessing bool
	isError      bool
	state        int
}

func NewDoorClient(doorId int64, name string, branchId int) *DoorClient {
	secKey := uuid.New().String()

	return &DoorClient{
		DoorId:       doorId,
		Name:         name,
		BranchId:     branchId,
		isOpen:       false,
		qrIn:         nil,
		qrOut:        nil,
		isprocessing: false,
		onlineTime:   time.Now(),
		secKey:       secKey,
		state:        code.IDEL,
	}
}

func (d *DoorClient) SetQRIn(client *QRClient) error {
	if client.CheckType() == "qr-in" {
		d.qrIn = client
		return nil
	}

	return errors.New("clinet need to be [in] type")
}

func (d *DoorClient) SetQROut(client *QRClient) error {
	if client.CheckType() == "qr-out" {
		d.qrOut = client
		return nil
	}
	return errors.New("clinet need to be [out] type")
}

func (d *DoorClient) SetIsProcessing(b bool) {
	d.isprocessing = b
}

func (d *DoorClient) CheckIsProcessing() bool {
	return d.isprocessing
}

func (d *DoorClient) SetIsOpen(b bool) {
	d.isOpen = b
}

func (d *DoorClient) IsOpen() bool {
	return d.isOpen
}

func (d *DoorClient) SetState(state int) {
	d.state = state
}

func (d *DoorClient) GetState() int {
	return d.state
}

func (d *DoorClient) GetSecKey() string {
	return d.secKey
}

func (d *DoorClient) GetOnlineTime() time.Time {
	return d.onlineTime
}

func (d *DoorClient) GetLastCmd() time.Time {
	return d.lastCmd
}

func (d *DoorClient) SetLastCmd(t time.Time) {
	d.lastCmd = t
}

func (d *DoorClient) GetQRIn() *QRClient {
	if d.qrIn == nil || !d.qrIn.CheckValid() {
		return nil
	}
	return d.qrIn
}

func (d *DoorClient) GetQROut() *QRClient {
	if d.qrOut == nil || !d.qrOut.CheckValid() {
		return nil
	}
	return d.qrOut
}

func (d *DoorClient) GenNewSecKey() {
	secKey := uuid.New().String()
	d.secKey = secKey
}

type QRClient struct {
	Id           string
	Typ          string
	BranchId     int
	doorId       int64
	isprocessing bool
	isError      bool
	onlineTime   time.Time
	state        int
	valid        bool
}

func NewQRClient(typ string, branchId int, Id string, door int64) *QRClient {
	return &QRClient{
		Id:           Id,
		Typ:          typ,
		BranchId:     branchId,
		doorId:       door,
		isprocessing: false,
		isError:      false,
		onlineTime:   time.Now(),
		state:        code.IDEL,
		valid:        true,
	}
}

func (q *QRClient) CheckType() string {
	return q.Typ
}

func (q *QRClient) CheckValid() bool {
	return q.valid
}

func (q *QRClient) SetValid(b bool) {
	q.valid = b
}

func (q *QRClient) SetIsProcessing(b bool) {
	q.isprocessing = b
}

func (q *QRClient) CheckIsProcessing() bool {
	return q.isprocessing
}

func (q *QRClient) SetIsError(b bool) {
	q.isError = b
}

func (q *QRClient) CheckIsError() bool {
	return q.isError
}

func (q *QRClient) SetState(state int) {
	q.state = state
}

func (q *QRClient) GetState() int {
	return q.state
}

func (q *QRClient) GetOnlineTime() time.Time {
	return q.onlineTime
}

type ClientState struct {
	DoorLocks map[string]*DoorClient
	QRDevice  map[string]*QRClient
	mut       sync.RWMutex
}

func NewClientState() *ClientState {
	return &ClientState{
		DoorLocks: make(map[string]*DoorClient, 5),
		QRDevice:  make(map[string]*QRClient, 10),
		mut:       sync.RWMutex{},
	}
}

func (c *ClientState) CreateDoorClient(clientId string, doorId int64, name string, branchId int) {
	c.mut.Lock()
	defer c.mut.Unlock()

	doorClient := NewDoorClient(doorId, name, branchId)
	c.DoorLocks[clientId] = doorClient
}

func (c *ClientState) GetDoorClient(clientId string) (*DoorClient, error) {
	c.mut.RLock()
	defer c.mut.RUnlock()

	res, ok := c.DoorLocks[clientId]
	if !ok {
		return nil, errors.New("door client not found")
	}

	return res, nil
}

func (c *ClientState) CreateQRClient(clientId string, typ string, branchId int, door int64) {
	c.mut.Lock()
	defer c.mut.Unlock()

	qrClient := NewQRClient(typ, branchId, clientId, door)
	c.QRDevice[clientId] = qrClient
}

func (c *ClientState) CreateQRClientWithDoorCheckin(clientId string, typ string, branchId int, door int64) {
	c.mut.Lock()
	defer c.mut.Unlock()

	qrClient := NewQRClient(typ, branchId, clientId, door)
	c.QRDevice[clientId] = qrClient
	c.WithDoorClient(door, func(d *DoorClient) {
		switch typ {
		case "qr-in":
			d.qrIn = qrClient
		case "qr-out":
			d.qrOut = qrClient
		default:
			log.Print("There is issue @ clientData typ")
		}
	})
}

func (c *ClientState) GetQRClient(clientId string) (*QRClient, error) {
	c.mut.RLock()
	defer c.mut.RUnlock()

	res, ok := c.QRDevice[clientId]
	if !ok {
		return nil, errors.New("qr client not found")
	}

	if !res.CheckValid() {
		return nil, errors.New("qr client is not valid")
	}

	return res, nil
}

func (c *ClientState) RemoveDoorClient(clientId string) {
	c.mut.Lock()
	defer c.mut.Unlock()
	if _, ok := c.DoorLocks[clientId]; !ok {
		return
	}

	delete(c.DoorLocks, clientId)
}

func (c *ClientState) RemoveQRClient(clientId string) {
	c.mut.Lock()
	defer c.mut.Unlock()

	qrClient, ok := c.QRDevice[clientId]
	if !ok {
		return
	}

	// Mark as invalid first
	qrClient.SetValid(false)

	// Remove QR client association from door clients
	for _, doorClient := range c.DoorLocks {
		if doorClient.qrIn != nil && doorClient.qrIn.Id == clientId {
			doorClient.qrIn = nil
		}
		if doorClient.qrOut != nil && doorClient.qrOut.Id == clientId {
			doorClient.qrOut = nil
		}
	}

	// Remove from map
	delete(c.QRDevice, clientId)
}

// SafelyAssociateQRClient safely associates a QR client with a door client
func (c *ClientState) SafelyAssociateQRClient(doorId int64, qrClientId string, qrType string) error {
	c.mut.Lock()
	defer c.mut.Unlock()

	// Find the door client
	var doorClient *DoorClient
	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			doorClient = door
			break
		}
	}

	if doorClient == nil {
		return fmt.Errorf("door client not found for door ID: %d", doorId)
	}

	// Find the QR client
	qrClient, ok := c.QRDevice[qrClientId]
	if !ok {
		return fmt.Errorf("QR client not found: %s", qrClientId)
	}

	switch qrType {
	case "qr-in":
		if doorClient.qrIn != nil {
			return fmt.Errorf("qr-in already exists for door %d", doorId)
		}
		doorClient.qrIn = qrClient
	case "qr-out":
		if doorClient.qrOut != nil {
			return fmt.Errorf("qr-out already exists for door %d", doorId)
		}
		doorClient.qrOut = qrClient
	default:
		return fmt.Errorf("invalid QR type: %s", qrType)
	}

	return nil
}

func (c *ClientState) GetDoorClientsByBranch(id int) []*DoorClient {
	c.mut.RLock()
	defer c.mut.RUnlock()

	var res []*DoorClient
	for _, door := range c.DoorLocks {
		if door.BranchId == id {
			res = append(res, door)
		}
	}

	return res
}

func (c *ClientState) GetQRClientsByBranch(id int) []*QRClient {
	c.mut.RLock()
	defer c.mut.RUnlock()
	var res []*QRClient

	for _, qr := range c.QRDevice {
		if qr.BranchId == id && qr.CheckValid() {
			res = append(res, qr)
		}
	}

	return res
}

func (c *ClientState) WithDoorClient(doorId int64, fn func(*DoorClient)) {
	c.mut.Lock() // or RLock if you only need read access
	defer c.mut.Unlock()
	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			fn(door) // Execute callback with original pointer while holding lock
			return
		}
	}
}

func (c *ClientState) GetDoorClientsQRINOUTAvalable(doorId int64, typ string) bool {
	c.mut.RLock()
	defer c.mut.RUnlock()

	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			switch typ {
			case "qr-in":
				if door.qrIn == nil {
					return true
				}
				return false
			case "qr-out":
				if door.qrOut == nil {
					return true
				}
				return false
			default:
				return false
			}
		}
	}
	return false
}

func (c *ClientState) CheckDoorExist(doorId int64) bool {
	c.mut.RLock()
	defer c.mut.RUnlock()

	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			return true
		}
	}
	return false
}
